'use client';

import React from 'react';
import { Card, Row, Col, Statistic, Button, List, Typography, Alert, Space, Tag } from 'antd';
import {
  DashboardOutlined,
  InboxOutlined,
  FileOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  UserOutlined
} from '@ant-design/icons';
import { useAuth } from '@/hooks/useAuth';
import { UserRole } from '@/types/user';
import { getUserRoleLabel, getUserRoleColor } from '@/lib/auth/client';
import FrontBoxConfig from '@/components/admin/FrontBoxConfig';

const { Title, Paragraph } = Typography;

export default function DashboardPage() {
  const { user } = useAuth();



  return (
    <div>
      <Title level={2}>仪表盘</Title>

      {/* 欢迎信息 */}
      {user && (
        <Alert
          message={
            <Space>
              <CheckCircleOutlined style={{ color: '#52c41a' }} />
              <span>欢迎访问管理后台，{user.name}！</span>
            </Space>
          }
          description={
            <Space direction="vertical" size="small">
              <div>
                <UserOutlined style={{ marginRight: 8 }} />
                当前角色：
                <Tag color={getUserRoleColor(user.role)} style={{ marginLeft: 8 }}>
                  {getUserRoleLabel(user.role)}
                </Tag>
              </div>
              <div>您拥有管理后台的完整访问权限，可以管理系统的各项功能。</div>
            </Space>
          }
          type="success"
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}
      
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic 
              title="盒型总数" 
              value={0} 
              prefix={<InboxOutlined />} 
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic 
              title="已发布盒型" 
              value={0} 
              prefix={<FileOutlined />} 
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic 
              title="草稿盒型" 
              value={0} 
              prefix={<FileOutlined />} 
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic 
              title="系统运行天数" 
              value={0} 
              prefix={<CalendarOutlined />} 
            />
          </Card>
        </Col>
      </Row>
      
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col xs={24} md={12}>
          <FrontBoxConfig compact />
        </Col>

        <Col xs={24} md={12}>
          <Card title="快速操作">
            <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
              <Button type="primary" size="middle">新建盒型</Button>
              <Button>导入盒型</Button>
              <Button>系统设置</Button>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
} 